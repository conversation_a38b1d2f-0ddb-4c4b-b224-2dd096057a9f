﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Process - Balkan Recruiters | Staff placement from the Balkans</title>
    <meta name="description" content="Transparent and efficient recruitment process – from needs analysis to employee integration. Learn how we place qualified workers from the Balkans.">
    <meta name="keywords" content="recruitment process, staff placement, worker placement, Balkan Recruiters, visa process">
    <link rel="canonical" href="https://www.balkanrecruiters.com/pages/process.html" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="../favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../favicon/favicon-16x16.png">
    <link rel="manifest" href="../favicon/site.webmanifest">
    
    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="Process - Balkan Recruiters" />
    <meta property="og:description" content="Transparent and efficient recruitment process – from needs analysis to employee integration. Learn how we place qualified workers from the Balkans." />
    <meta property="og:url" content="https://www.balkanrecruiters.com/pages/process.html" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://www.balkanrecruiters.com/photos/logo.png" />
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Process - Balkan Recruiters" />
    <meta name="twitter:description" content="Transparent and efficient recruitment process – from needs analysis to employee integration. Learn how we place qualified workers from the Balkans." />
    <meta name="twitter:image" content="https://www.balkanrecruiters.com/photos/logo.png" />
    
    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow">
    <meta name="author" content="Balkan Recruiters">
    <meta name="geo.region" content="BA">
    <meta name="geo.placename" content="Jelah">
    <meta name="geo.position" content="44.6500;17.9600">
    
    <!-- Language Alternates -->
    <link rel="alternate" href="https://balkanrecruiters.de/pages/prozess.html" hreflang="de-DE">
    <link rel="alternate" href="https://balkanrecruiters.com/pages/process.html" hreflang="en-US">
    <link rel="alternate" href="https://balkanrecruiters.com/pages/process.html" hreflang="x-default">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
    
    <!-- Structured data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Process - Balkan Recruiters",
      "description": "Transparent and efficient recruitment process – from needs analysis to employee integration. Learn how we place qualified workers from the Balkans.",
      "publisher": {
        "@type": "Organization",
        "name": "Balkan Recruiters",
        "logo": {
          "@type": "ImageObject",
          "url": "https://www.balkanrecruiters.com/photos/logo.png"
        }
      },
      "url": "https://www.balkanrecruiters.com/pages/process.html"
    }
    </script>
    
    <!-- Additional Schema.org markup -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "Recruitment Process - Balkan Recruiters",
      "description": "Transparent and efficient recruitment process – from needs analysis to employee integration.",
      "step": [
        {
          "@type": "HowToStep",
          "name": "Needs analysis",
          "text": "We thoroughly analyze your workforce needs"
        },
        {
          "@type": "HowToStep",
          "name": "Candidate selection",
          "text": "We select the best candidates from our database"
        },
        {
          "@type": "HowToStep",
          "name": "Interviews",
          "text": "We organize interviews with selected candidates"
        },
        {
          "@type": "HowToStep",
          "name": "Visa process",
          "text": "We help with obtaining work visas"
        },
        {
          "@type": "HowToStep",
          "name": "Integration",
          "text": "We provide support for worker integration"
        }
      ],
      "totalTime": "P30D"
    }
    </script>

    <!-- Social Media Verification -->
    <meta name="facebook-domain-verification" content="your-code">
    <meta name="google-site-verification" content="your-code">

    <style>
        [x-cloak] { display: none !important; }
        
        /* Optional: Add smooth transitions for dropdowns */
        .dropdown-transition {
            transition: opacity 0.15s ease-out, transform 0.15s ease-out;
        }
    </style>
    <script src="https://unpkg.com/scrollreveal"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#023679',
                        secondary: '#0369a1',
                        accent: '#3b82f6'
                    }
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for journey steps animation
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, {
                threshold: 0.1
            });

            // Observe all journey steps
            document.querySelectorAll('.journey-step').forEach((step) => {
                observer.observe(step);
            });
        });
    </script>

    <!-- LinkedIn Insight Tag -->
    <script type="text/javascript">
        _linkedin_partner_id = "8507321";
        window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
        window._linkedin_data_partner_ids.push(_linkedin_partner_id);
    </script>
    <script type="text/javascript">
        (function(l) {
            if (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])};
            window.lintrk.q=[]}
            var s = document.getElementsByTagName("script")[0];
            var b = document.createElement("script");
            b.type = "text/javascript";b.async = true;
            b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
            s.parentNode.insertBefore(b, s);
        })(window.lintrk);
    </script>
    <noscript>
        <img height="1" width="1" style="display:none;" alt="" src="https://px.ads.linkedin.com/collect/?pid=8507321&fmt=gif" />
    </noscript>

    <!-- Microsoft Ads UET Tracking -->
    <script>
        (function(w,d,t,r,u)
        {
            var f,n,i;
            w[u]=w[u]||[],f=function()
            {
                var o={ti:"187206265", enableAutoSpaTracking: true};
                o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")
            },
            n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function()
            {
                var s=this.readyState;
                s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)
            },
            i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)
        })
        (window,document,"script","//bat.bing.com/bat.js","uetq");
    </script></head>
<body class="bg-gray-50" x-data="{ mobileMenuOpen: false }">
    <header>
        <nav class="bg-white shadow-sm fixed w-full top-0 z-50">
            <div class="container mx-auto px-4 py-5">
                <!-- Mobile Menu Button -->
                <div class="flex items-center justify-between lg:hidden">
                    <a href="../index.html">
                        <img src="../photos/logo.png" alt="BalkanRecruiters Logo" class="h-12">
                    </a>
                    <button class="text-gray-600 hover:text-primary" @click="mobileMenuOpen = !mobileMenuOpen">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:flex items-center">
                    <a href="../index.html" class="absolute left-4">
                        <img src="../photos/logo.png" alt="BalkanRecruiters Logo" class="h-12">
                    </a>
                    <ul class="flex space-x-8 mx-auto">
                        <li><a href="services.html" class="nav-link text-gray-700">Services</a></li>
                        <li><a href="system.html" class="nav-link text-gray-700">System</a></li>
                        <li><a href="process.html" class="nav-link text-gray-700 active">Process</a></li>
                        <li><a href="about-us.html" class="nav-link text-gray-700">About Us</a></li>
                        <li><a href="faq.html" class="nav-link text-gray-700">FAQ</a></li>
                        <li><a href="kontakt.html" class="nav-link text-gray-700">Contact</a></li>
                    </ul>
                    <div class="flex items-center space-x-6 absolute right-4">
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-1 text-gray-700 hover:text-primary">
                                <span class="font-medium text-primary">EN</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div x-cloak x-show="open" x-transition.opacity @click.away="open = false" 
                                 class="absolute right-0 mt-2 py-2 w-16 bg-white rounded-lg shadow-xl dropdown-transition">
                                <a href="https://balkanrecruiters.de/pages/prozess.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 text-center">DE</a>
                            </div>
                        </div>
                        <a href="https://crm.ssc.ba/" target="_blank" class="text-primary hover:text-white border-2 border-primary hover:bg-primary px-4 py-2 rounded-md transition-all duration-200">Partner Login</a>
                    </div>
                </div>

                <!-- Mobile Menu -->
                <div x-show="mobileMenuOpen" class="lg:hidden">
                    <ul class="mt-4 space-y-2">
                        <li><a href="services.html" class="block text-gray-700 hover:text-primary py-2">Services</a></li>
                        <li><a href="system.html" class="block text-gray-700 hover:text-primary py-2">System</a></li>
                        <li><a href="process.html" class="block text-gray-700 hover:text-primary py-2">Process</a></li>
                        <li><a href="about-us.html" class="block text-gray-700 hover:text-primary py-2">About Us</a></li>
                        <li><a href="faq.html" class="block text-gray-700 hover:text-primary py-2">FAQ</a></li>
                        <li><a href="kontakt.html" class="block text-gray-700 hover:text-primary py-2">Contact</a></li>
                    </ul>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex space-x-4">
                            <a href="https://balkanrecruiters.de/pages/prozess.html" class="text-gray-700 hover:text-primary font-medium">DE</a>
                        </div>
                        <a href="https://crm.ssc.ba/" target="_blank" class="text-primary hover:text-white border-2 border-primary hover:bg-primary px-4 py-2 rounded-md transition-all duration-200">Partner Login</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section with Background Image -->
    <section class="relative min-h-[80vh] flex items-center mt-[60px]">
        <!-- Background Image -->
        <div class="absolute inset-0 w-full h-full overflow-hidden">
            <img src="../photos/proces.webp" alt="Process Hero" class="w-full h-full object-cover md:object-center object-left">
        </div>

        <!-- Content -->
        <div class="relative container mx-auto px-4 z-10">
            <div class="w-full lg:w-1/2">
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">Simple and efficient recruitment process</h1>
                <p class="text-base sm:text-lg md:text-xl lg:text-2xl text-white">From first contact to employment – we take care of everything.</p>
            </div>
        </div>
    </section>

    <!-- Journey Map Section -->
    <section class="journey-map relative py-12 pt-[100px]">
        <div class="container max-w-6xl mx-auto px-4">
            <div class="space-y-32 relative">
                <!-- Step 1 -->
                <div class="journey-step" data-step="1">
                    <div class="flex">
                        <div class="step-number">1</div>
                        <div class="content-card">
                            <div class="content-text">
                                <h3 class="text-2xl font-bold text-primary mb-4">Needs analysis</h3>
                                <p class="text-gray-600">We analyze your requirements and develop a customized candidate profile.</p>
                            </div>
                            <div class="content-image" style="background-image: url('../photos/Process/7.png');"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="journey-step" data-step="2">
                    <div class="flex">
                        <div class="step-number">2</div>
                        <div class="content-card">
                            <div class="content-text">
                                <h3 class="text-2xl font-bold text-primary mb-4">Candidate search</h3>
                                <p class="text-gray-600">Our network and modern recruitment tools lead us to the best talents.</p>
                            </div>
                            <div class="content-image" style="background-image: url('../photos/Process/8.png');"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="journey-step" data-step="3">
                    <div class="flex">
                        <div class="step-number">3</div>
                        <div class="content-card">
                            <div class="content-text">
                                <h3 class="text-2xl font-bold text-primary mb-4">Pre-selection</h3>
                                <p class="text-gray-600">We present you only candidates who perfectly match your requirements.</p>
                            </div>
                            <div class="content-image" style="background-image: url('../photos/Process/9.png');"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="journey-step" data-step="4">
                    <div class="flex">
                        <div class="step-number">4</div>
                        <div class="content-card">
                            <div class="content-text">
                                <h3 class="text-2xl font-bold text-primary mb-4">Interviews</h3>
                                <p class="text-gray-600">Efficient online interviews or in-person testing – you decide.</p>
                            </div>
                            <div class="content-image" style="background-image: url('../photos/Process/10.png');"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="journey-step" data-step="5">
                    <div class="flex">
                        <div class="step-number">5</div>
                        <div class="content-card">
                            <div class="content-text">
                                <h3 class="text-2xl font-bold text-primary mb-4">Contract signing</h3>
                                <p class="text-gray-600">We guide you to the conclusion of the employment contract with your ideal candidate.</p>
                            </div>
                            <div class="content-image" style="background-image: url('../photos/Process/11.png');"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 6 -->
                <div class="journey-step" data-step="6">
                    <div class="flex">
                        <div class="step-number">6</div>
                        <div class="content-card">
                            <div class="content-text">
                                <h3 class="text-2xl font-bold text-primary mb-4">Visa process</h3>
                                <p class="text-gray-600">Our team takes over the complete visa application process – fast and reliable.</p>
                            </div>
                            <div class="content-image" style="background-image: url('../photos/Process/12.png');"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 7 -->
                <div class="journey-step" data-step="7">
                    <div class="flex">
                        <div class="step-number">7</div>
                        <div class="content-card">
                            <div class="content-text">
                                <h3 class="text-2xl font-bold text-primary mb-4">Integration</h3>
                                <p class="text-gray-600">From finding accommodation to health insurance – we support your new employees at every step.</p>
                            </div>
                            <div class="content-image" style="background-image: url('../photos/Process/13.png');"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-900 to-primary text-white pt-16 pb-8">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <!-- Company Info -->
                <div class="space-y-4">
                    <img src="../photos/logo.png" alt="BalkanRecruiters Logo" class="h-12 mb-4">
                    <p class="text-gray-300">Your reliable partner for finding qualified workers from the Balkans.</p>
                    <!-- BARMER Partnership -->
                    <div class="mt-6 pt-4 border-t border-gray-700">
                        <p class="text-gray-400 text-sm mb-3">In cooperation with:</p>
                        <div class="flex items-center">
                            <img src="../photos/partners/barmer.svg" alt="BARMER Health Insurance" class="h-8">
                        </div>
                        <p class="text-gray-400 text-xs mt-2">Health insurance for all placed professionals</p>
                    </div>                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="services.html" class="text-gray-300 hover:text-white transition-colors">Services</a></li>
                        <li><a href="system.html" class="text-gray-300 hover:text-white transition-colors">System</a></li>
                        <li><a href="process.html" class="text-gray-300 hover:text-white transition-colors">Process</a></li>
                        <li><a href="about-us.html" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="faq.html" class="text-gray-300 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="kontakt.html" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2">
                        <li><a href="privacy-policy.html" class="text-gray-300 hover:text-white transition-colors">Privacy</a></li>
                        <li><a href="impressum.html" class="text-gray-300 hover:text-white transition-colors">Impressum</a></li>
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-start space-x-2">
                            <svg class="w-5 h-5 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <div class="flex flex-col">
                                <span>Rosulje 38</span>
                                <span>74264 Jelah</span>
                                <span>Bosnia and Herzegovina</span>
                            </div>
                        </li>
                        <li class="flex items-center space-x-2">
                            <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 pt-8 text-center text-gray-400">
                <p>&copy;2025 BalkanRecruiters. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>


